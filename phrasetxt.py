from splitter import RecursiveCharacterTextSplitter
from qdrant_client import QdrantClient, models
import re

client = QdrantClient(host="localhost", port=6333)
VECTOR_SIZE = 384
DISTANCE_METRIC = models.Distance.COSINE
DEFAULT_VECTOR_NAME = "fast-bge-small-en"

def embedtxt(txt: str, collection_name: str):
    sanitized_collection_name = re.sub(r'[^a-zA-Z0-9_-]', '_', collection_name)
    
    client.recreate_collection(
        collection_name=sanitized_collection_name,
        vectors_config={
            DEFAULT_VECTOR_NAME: models.VectorParams(
                size=VECTOR_SIZE,
                distance=DISTANCE_METRIC,
            )
        },
    )

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=100
    )
    chunks = splitter.split_text(txt)
    

    client.add(
        collection_name=sanitized_collection_name,
        documents=chunks,
    )
    return sanitized_collection_name
