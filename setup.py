#!/usr/bin/env python3
"""
Setup script for AI Legal Document Analyzer
Installs dependencies and downloads required models
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🏛️ AI Legal Document Analyzer Setup")
    print("=" * 50)
    
    # Install Python dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        print("⚠️  Some dependencies may have failed to install. Please check manually.")
    
    # Download SpaCy model
    if not run_command("python -m spacy download en_core_web_sm", "Downloading SpaCy English model"):
        print("⚠️  SpaCy model download failed. Please run manually: python -m spacy download en_core_web_sm")
    
    # Download NLTK data
    print("\n🔄 Downloading NLTK data...")
    try:
        import nltk
        nltk.download('vader_lexicon', quiet=True)
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK data downloaded successfully")
    except Exception as e:
        print(f"⚠️  NLTK data download failed: {e}")
    
    # Check if Qdrant is running
    print("\n🔄 Checking Qdrant vector database...")
    try:
        import requests
        response = requests.get("http://localhost:6333/health", timeout=5)
        if response.status_code == 200:
            print("✅ Qdrant is running and accessible")
        else:
            print("⚠️  Qdrant is not responding correctly")
    except Exception:
        print("⚠️  Qdrant is not running. Please start it with:")
        print("   docker run -p 6333:6333 qdrant/qdrant")
    
    # Verify installations
    print("\n🔍 Verifying installations...")
    
    required_packages = [
        'streamlit', 'torch', 'transformers', 'spacy', 
        'qdrant_client', 'pandas', 'plotly', 'nltk', 
        'wordcloud', 'matplotlib'
    ]
    
    failed_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    # Final status
    print("\n" + "=" * 50)
    if not failed_packages:
        print("🎉 Setup completed successfully!")
        print("\nTo start the application, run:")
        print("   streamlit run app.py")
    else:
        print("⚠️  Setup completed with some issues:")
        print(f"   Failed packages: {', '.join(failed_packages)}")
        print("\nPlease install missing packages manually and try again.")
    
    print("\n📋 Next steps:")
    print("1. Ensure Qdrant is running: docker run -p 6333:6333 qdrant/qdrant")
    print("2. Update Google AI API key in rag.py")
    print("3. Run the application: streamlit run app.py")

if __name__ == "__main__":
    main()
