"""
Configuration file for AI Legal Document Analyzer
Centralized settings for models, UI, and analysis parameters
"""

import os

# =============================================================================
# MODEL CONFIGURATIONS
# =============================================================================

# Hugging Face Model IDs
SUMMARIZER_MODEL = "ramnotfound/legal-summarizer-t5-billsum"
NER_MODEL = "dbmdz/bert-large-cased-finetuned-conll03-english"
SPACY_MODEL = "en_core_web_sm"

# Google AI Configuration
GOOGLE_AI_API_KEY = "AIzaSyD5zkhwgtfpfMeMqF4Jz2LRd7Loxa59ZDM"  # Replace with your API key
GOOGLE_AI_MODEL = "gemini-2.5-flash"

# Qdrant Configuration
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
VECTOR_SIZE = 384
DISTANCE_METRIC = "COSINE"
DEFAULT_VECTOR_NAME = "fast-bge-small-en"

# =============================================================================
# ANALYSIS CONFIGURATIONS
# =============================================================================

# Text Processing
MAX_TEXT_LENGTH = 10000  # Maximum text length for processing
CHUNK_SIZE = 1000        # Text chunk size for embeddings
CHUNK_OVERLAP = 100      # Overlap between chunks

# Summary Settings
DEFAULT_SUMMARY_LENGTH = 256
MIN_SUMMARY_LENGTH = 100
MAX_SUMMARY_LENGTH = 500

# Entity Extraction
ENTITY_CONFIDENCE_THRESHOLD = 0.8
MAX_ENTITIES_PER_TYPE = 50
INCLUDE_ENTITY_CONTEXT = True

# Sentiment Analysis
SENTIMENT_BATCH_SIZE = 50  # Number of sentences to analyze

# =============================================================================
# LEGAL PATTERNS
# =============================================================================

# Enhanced legal entity patterns
LEGAL_ENTITY_PATTERNS = {
    "Case_Numbers": [
        r"\b(?:Civil Appeal|Criminal Appeal|Writ Petition|PIL|SLP|CA|CRL\.A|W\.P)\s*(?:No\.?)?\s*(\d+(?:[-/]\d+)*(?:\s*of\s*\d{4})?)\b",
        r"\b(?:FIR|F\.I\.R)\s*(?:No\.?)?\s*(\d+(?:[-/]\d+)*)\b",
        r"\b(?:Complaint|Comp)\s*(?:No\.?)?\s*(\d+(?:[-/]\d+)*)\b"
    ],
    
    "Court_Names": [
        r"\b(?:Supreme Court|High Court|District Court|Sessions Court|Magistrate Court|Tribunal|Commission|NCLT|NCLAT)\s+(?:of\s+)?[\w\s]+\b",
        r"\b(?:Hon'ble|Honourable)\s+(?:Mr\.|Ms\.|Justice|Chief Justice)\s+[\w\s\.]+\b"
    ],
    
    "Legal_Citations": [
        r"\b\d{4}\s+\w+\s+\d+\b",
        r"\b\(\d{4}\)\s+\d+\s+\w+\s+\d+\b",
        r"\b(?:AIR|SCC|SCR|DLT|BomCR|CalLT|KLT|MLJ)\s+\d{4}\s+\w*\s*\d+\b"
    ],
    
    "Statutes_and_Sections": [
        r"\b(?:Section|Sec\.|Article|Art\.|Rule|Regulation|Clause)\s+\d+(?:\([a-zA-Z0-9]+\))?(?:\s+(?:of|under)\s+.+?Act\b)?\b",
        r"\b(?:Chapter|Part)\s+[IVX]+(?:\s+of\s+.+?Act\b)?\b",
        r"\b.+?Act,?\s+\d{4}\b"
    ],
    
    "Dates": [
        r"\b\d{1,2}[./]\d{1,2}[./]\d{2,4}\b",
        r"\b\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s*,?\s+\d{4}\b",
        r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2}(?:st|nd|rd|th)?\s*,?\s+\d{4}\b"
    ],
    
    "Money_Amounts": [
        r"\b(?:Rs\.?|INR|USD|\$|₹)\s*\d+(?:,\d{2,3})*(?:\.\d{2})?\b",
        r"\b\d+(?:,\d{2,3})*\s*(?:crores?|lakhs?|thousands?|millions?|billions?)\b",
        r"\bRupees\s+[\w\s]+only\b"
    ],
    
    "Legal_Entities": [
        r"\b(?:Plaintiff|Defendant|Appellant|Respondent|Petitioner|Applicant|Complainant|Accused)\b",
        r"\b(?:Advocate|Counsel|Senior Advocate|Additional Solicitor General|Solicitor General|Attorney General)\b"
    ]
}

# Legal clause patterns
LEGAL_CLAUSE_PATTERNS = {
    "Jurisdiction": [
        r"(?:jurisdiction|competent court|courts?\s+(?:of|in|at)|subject to the jurisdiction).*?(?:\.|;|$)",
        r"(?:exclusive jurisdiction|non-exclusive jurisdiction).*?(?:\.|;|$)"
    ],
    
    "Governing_Law": [
        r"(?:governing law|governed by|applicable law|subject to.*?law).*?(?:\.|;|$)",
        r"(?:laws of|Indian law|foreign law).*?(?:\.|;|$)"
    ],
    
    "Limitation_of_Liability": [
        r"(?:limitation of liability|liable|damages|loss|injury).*?(?:\.|;|$)",
        r"(?:shall not be liable|limited liability|maximum liability).*?(?:\.|;|$)"
    ],
    
    "Confidentiality": [
        r"(?:confidential|non-disclosure|proprietary|trade secret).*?(?:\.|;|$)",
        r"(?:maintain confidentiality|keep confidential|disclose).*?(?:\.|;|$)"
    ],
    
    "Termination": [
        r"(?:termination|terminate|dissolution|expire|end).*?(?:\.|;|$)",
        r"(?:breach|default|violation).*?(?:\.|;|$)"
    ],
    
    "Indemnification": [
        r"(?:indemnify|indemnification|hold harmless|defend).*?(?:\.|;|$)",
        r"(?:claims|damages|losses|expenses).*?(?:\.|;|$)"
    ],
    
    "Force_Majeure": [
        r"(?:force majeure|act of god|unforeseeable|beyond.*?control).*?(?:\.|;|$)",
        r"(?:natural disaster|war|terrorism|government action).*?(?:\.|;|$)"
    ],
    
    "Arbitration": [
        r"(?:arbitration|arbitrator|dispute resolution|mediation).*?(?:\.|;|$)",
        r"(?:arbitral tribunal|arbitration proceedings).*?(?:\.|;|$)"
    ],
    
    "Intellectual_Property": [
        r"(?:intellectual property|copyright|trademark|patent|trade mark).*?(?:\.|;|$)",
        r"(?:proprietary rights|ownership|license).*?(?:\.|;|$)"
    ],
    
    "Payment_Terms": [
        r"(?:payment|remuneration|consideration|fees?|salary|wages).*?(?:\.|;|$)",
        r"(?:due date|payment terms|interest|penalty).*?(?:\.|;|$)"
    ]
}

# =============================================================================
# UI CONFIGURATIONS
# =============================================================================

# Streamlit Configuration
PAGE_TITLE = "AI Legal Document Analyzer"
PAGE_ICON = "⚖️"
LAYOUT = "wide"
SIDEBAR_STATE = "expanded"

# Color Scheme
ENTITY_COLORS = {
    'PERSON': '#e3f2fd',
    'PER': '#e3f2fd',
    'ORG': '#f3e5f5',
    'ORGANIZATION': '#f3e5f5',
    'GPE': '#e8f5e8',
    'LOC': '#e8f5e8',
    'DATE': '#fff3e0',
    'TIME': '#fff3e0',
    'MONEY': '#fce4ec',
    'Money_Amounts': '#fce4ec',
    'LAW': '#e0f2f1',
    'Legal_Citations': '#e0f2f1',
    'Case_Numbers': '#e1f5fe',
    'Court_Names': '#f1f8e9',
    'Statutes_and_Sections': '#fff8e1'
}

# Analysis Options
DEFAULT_ANALYSIS_OPTIONS = [
    "Summary",
    "Named Entities", 
    "Legal Clauses"
]

ALL_ANALYSIS_OPTIONS = [
    "Summary",
    "Named Entities",
    "Legal Clauses", 
    "Sentiment Analysis",
    "Document Structure",
    "Word Cloud"
]

# =============================================================================
# FILE PROCESSING
# =============================================================================

# Supported file types
SUPPORTED_FILE_TYPES = ["pdf", "docx", "txt"]

# File size limits (in MB)
MAX_FILE_SIZE = 50

# Text extraction settings
PDF_EXTRACTION_METHOD = "PyPDF2"  # Options: "PyPDF2", "pymupdf"
DOCX_EXTRACTION_METHOD = "python-docx"

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Caching
ENABLE_MODEL_CACHING = True
CACHE_TTL = 3600  # Cache time-to-live in seconds

# Processing limits
MAX_CONCURRENT_REQUESTS = 5
REQUEST_TIMEOUT = 300  # seconds

# Memory management
CLEAR_CACHE_ON_UPLOAD = True
MAX_MEMORY_USAGE = 2048  # MB

# =============================================================================
# LOGGING AND DEBUGGING
# =============================================================================

# Logging configuration
LOG_LEVEL = "INFO"  # Options: DEBUG, INFO, WARNING, ERROR
LOG_FILE = "legal_analyzer.log"
ENABLE_CONSOLE_LOGGING = True

# Debug settings
DEBUG_MODE = False
SHOW_PROCESSING_TIME = True
SHOW_MODEL_INFO = True

# =============================================================================
# EXPORT SETTINGS
# =============================================================================

# Export formats
SUPPORTED_EXPORT_FORMATS = ["json", "csv", "txt", "pdf"]

# Export options
INCLUDE_METADATA = True
INCLUDE_CONFIDENCE_SCORES = True
INCLUDE_TIMESTAMPS = True

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# API Security
RATE_LIMITING = True
MAX_REQUESTS_PER_MINUTE = 60

# Data Privacy
STORE_UPLOADED_FILES = False
AUTO_DELETE_AFTER_SESSION = True
ANONYMIZE_LOGS = True

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_config(key, default=None):
    """Get configuration value with fallback to environment variable"""
    return os.getenv(key, default)

def update_config(key, value):
    """Update configuration value"""
    globals()[key] = value

def validate_config():
    """Validate configuration settings"""
    errors = []
    
    if not GOOGLE_AI_API_KEY or GOOGLE_AI_API_KEY == "your-api-key-here":
        errors.append("Google AI API key not configured")
    
    if CHUNK_SIZE <= 0:
        errors.append("Chunk size must be positive")
    
    if ENTITY_CONFIDENCE_THRESHOLD < 0 or ENTITY_CONFIDENCE_THRESHOLD > 1:
        errors.append("Entity confidence threshold must be between 0 and 1")
    
    return errors

if __name__ == "__main__":
    # Validate configuration on import
    config_errors = validate_config()
    if config_errors:
        print("Configuration errors found:")
        for error in config_errors:
            print(f"- {error}")
    else:
        print("Configuration validated successfully")
