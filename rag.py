from qdrant_client import <PERSON>drant<PERSON><PERSON>
from google import generativeai as genai
import re
from PIL import Image
import io
import os

prompt_template = """
You are a helpful assistant who answers user query from the legal context provided.\n
Please respond with clear, friendly, and concise answers.\n
also use normal human tone instead of legal tone\n
response in markdown format.\n

Context: {search_result}.

User: {query}
"""

# NOTE: The API key is now expected to be set as an environment variable 'GOOGLE_API_KEY'.
# This is a more secure practice than hardcoding keys in the source code.
try:
    client = genai.Client() # The client will automatically find the GOOGLE_API_KEY environment variable.
except Exception as e:
    print(f"Error initializing Google AI Client: {e}")
    print("Please ensure the GOOGLE_API_KEY environment variable is set correctly.")
    client = None # Set client to None if initialization fails

qdrant_client = QdrantClient(host="localhost", port=6333)


def ai_response(query: str, collection_name: str):
    """Generates a response based on a text query and a document context."""
    if not client:
        yield "Google AI Client is not configured. Please check your API key."
        return

    sanitized_collection_name = re.sub(r'[^a-zA-Z0-9_-]', '_', collection_name)
    if query:
        search_result = qdrant_client.query(
            collection_name=sanitized_collection_name,
            query_text=query,
        )
        
        prompt = prompt_template.format(search_result=search_result, query=query)
        
        response = client.generate_content(
            model="gemini-2.5-flash", 
            contents=prompt, 
            stream=True
        )
        for chunk in response:
            if chunk.text:
                yield chunk.text

def ai_image_response(query: str, image_bytes: bytes):
    """Generates a response based on a text query and an image."""
    if not client:
        yield "Google AI Client is not configured. Please check your API key."
        return
        
    try:
        img = Image.open(io.BytesIO(image_bytes))
        
        prompt_parts = [
            query, 
            img
        ]

        response = client.generate_content(
            model="gemini-2.5-flash", # Gemini 1.5 Flash is multimodal
            contents=prompt_parts, 
            stream=True
        )
        for chunk in response:
            if chunk.text:
                yield chunk.text

    except Exception as e:
        yield f"An error occurred while processing the image: {e}"