from qdrant_client import Q<PERSON>nt<PERSON><PERSON>
from fastembed import TextEmbedding
from splitter import RecursiveCharacterTextSplitter
import pymupdf4llm
from google import genai
from google.genai import types
import re

prompt = """
You are a helpful assistant who answers user query from the legal context provided.\n
Please respond with clear, friendly, and concise answers.\n
also use normal human tone instead of legal tone\n
response in markdown format.\n

Context: {search_result}.

User: {query}
"""


client = QdrantClient(host="localhost", port=6333)
genai_client = genai.Client(api_key="AIzaSyD5zkhwgtfpfMeMqF4Jz2LRd7Loxa59ZDM")

def ai_response(query: str,collection_name: str):
    sanitized_collection_name = re.sub(r'[^a-zA-Z0-9_-]', '_', collection_name)
    if query:
        search_result = client.query(
        collection_name=sanitized_collection_name,
        query_text=query,
        )
        response = genai_client.models.generate_content_stream(
        model="gemini-2.5-flash",
        contents=[prompt.format(search_result=search_result, query=query)],
        )
        full_response = str()
        for chunk in response:
            if chunk.text is not None:
                full_response += chunk.text
                yield chunk.text