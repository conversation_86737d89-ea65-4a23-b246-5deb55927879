from qdrant_client import QdrantClient
import re
import os

prompt_template = """
You are a helpful assistant who answers user query from the legal context provided.
Please respond with clear, friendly, and concise answers.
Also use normal human tone instead of legal tone.
Response in markdown format.

Context: {search_result}

User: {query}
"""

# Simple fallback response system
client_configured = False

try:
    import google.generativeai as genai
    api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyD5zkhwgtfpfMeMqF4Jz2LRd7Loxa59ZDM')
    genai.configure(api_key=api_key)
    client_configured = True
    print("✅ Google AI configured successfully")
except Exception as e:
    print(f"⚠️ Google AI not available: {e}")
    print("Using fallback response system")

qdrant_client = QdrantClient(host="localhost", port=6333)


def ai_response(query: str, collection_name: str):
    """Generates a response based on a text query and a document context."""
    sanitized_collection_name = re.sub(r'[^a-zA-Z0-9_-]', '_', collection_name)

    if not query:
        yield "Please provide a question."
        return

    try:
        # Get search results from Qdrant
        search_result = qdrant_client.query(
            collection_name=sanitized_collection_name,
            query_text=query,
        )

        if client_configured:
            try:
                # Use Google AI if available
                import google.generativeai as genai
                model = genai.GenerativeModel('gemini-2.5-flash')
                prompt = prompt_template.format(search_result=search_result, query=query)
                response = model.generate_content(prompt, stream=True)

                for chunk in response:
                    if chunk.text:
                        yield chunk.text
                return
            except Exception as e:
                print(f"Google AI error: {e}")

        # Fallback response using search results
        if search_result:
            context_text = str(search_result)[:1000]  # Limit context
            yield f"Based on the document context:\n\n{context_text}\n\n"
            yield f"**Answer to your question:** {query}\n\n"
            yield "I found relevant information in the document. The context above contains details related to your query. "
            yield "For more specific analysis, please ensure Google AI is properly configured."
        else:
            yield "No relevant information found in the document for your query."

    except Exception as e:
        yield f"Error processing your request: {e}"

def ai_image_response(query: str, image_bytes: bytes):
    """Generates a response based on a text query and an image."""
    if not client_configured:
        yield "Google AI Client is not configured for image analysis. Please check your API key."
        return

    try:
        from PIL import Image
        import io
        import google.generativeai as genai

        img = Image.open(io.BytesIO(image_bytes))
        model = genai.GenerativeModel('gemini-2.5-flash')

        prompt_parts = [query, img]
        response = model.generate_content(prompt_parts, stream=True)

        for chunk in response:
            if chunk.text:
                yield chunk.text

    except Exception as e:
        yield f"An error occurred while processing the image: {e}"