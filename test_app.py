#!/usr/bin/env python3
"""
Test script to verify the AI Legal Document Analyzer works correctly
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import torch
        print("✅ PyTorch imported successfully")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        from transformers import T5ForConditionalGeneration, T5Tokenizer
        print("✅ Transformers imported successfully")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        import spacy
        print("✅ SpaCy imported successfully")
    except ImportError as e:
        print(f"❌ SpaCy import failed: {e}")
        return False
    
    try:
        from qdrant_client import QdrantClient
        print("✅ Qdrant client imported successfully")
    except ImportError as e:
        print(f"❌ Qdrant client import failed: {e}")
        return False
    
    try:
        import pandas as pd
        import plotly.express as px
        import nltk
        from wordcloud import WordCloud
        import matplotlib.pyplot as plt
        print("✅ Data science libraries imported successfully")
    except ImportError as e:
        print(f"❌ Data science libraries import failed: {e}")
        return False
    
    return True

def test_models():
    """Test if models can be loaded"""
    print("\n🧪 Testing model loading...")
    
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        print("✅ SpaCy model loaded successfully")
    except Exception as e:
        print(f"❌ SpaCy model loading failed: {e}")
        return False
    
    try:
        from nltk.sentiment import SentimentIntensityAnalyzer
        sia = SentimentIntensityAnalyzer()
        print("✅ NLTK sentiment analyzer loaded successfully")
    except Exception as e:
        print(f"❌ NLTK sentiment analyzer failed: {e}")
        return False
    
    return True

def test_qdrant_connection():
    """Test Qdrant connection"""
    print("\n🧪 Testing Qdrant connection...")
    
    try:
        from qdrant_client import QdrantClient
        client = QdrantClient(host="localhost", port=6333)
        # Try to get collections (this will fail if Qdrant is not running)
        collections = client.get_collections()
        print("✅ Qdrant connection successful")
        return True
    except Exception as e:
        print(f"❌ Qdrant connection failed: {e}")
        print("   Make sure Qdrant is running: docker run -p 6333:6333 qdrant/qdrant")
        return False

def test_google_ai():
    """Test Google AI configuration"""
    print("\n🧪 Testing Google AI configuration...")
    
    try:
        import google.generativeai as genai
        import os
        
        api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyD5zkhwgtfpfMeMqF4Jz2LRd7Loxa59ZDM')
        if api_key and api_key != 'your-api-key-here':
            genai.configure(api_key=api_key)
            print("✅ Google AI configured successfully")
            return True
        else:
            print("⚠️ Google AI API key not configured")
            return False
    except Exception as e:
        print(f"❌ Google AI configuration failed: {e}")
        return False

def test_app_modules():
    """Test if app modules can be imported"""
    print("\n🧪 Testing app modules...")
    
    try:
        from rag import ai_response
        print("✅ RAG module imported successfully")
    except ImportError as e:
        print(f"❌ RAG module import failed: {e}")
        return False
    
    try:
        from phrasetxt import embedtxt
        print("✅ Phrase text module imported successfully")
    except ImportError as e:
        print(f"❌ Phrase text module import failed: {e}")
        return False
    
    try:
        from splitter import RecursiveCharacterTextSplitter
        print("✅ Text splitter module imported successfully")
    except ImportError as e:
        print(f"❌ Text splitter module import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test text processing
        from nltk.tokenize import word_tokenize, sent_tokenize
        test_text = "This is a test sentence. This is another sentence."
        words = word_tokenize(test_text)
        sentences = sent_tokenize(test_text)
        print(f"✅ Text tokenization works: {len(words)} words, {len(sentences)} sentences")
    except Exception as e:
        print(f"❌ Text processing failed: {e}")
        return False
    
    try:
        # Test regex patterns
        import re
        test_text = "CIVIL APPEAL NO. 7527-7528 OF 2023"
        pattern = r"\b(?:CIVIL APPEAL|CRIMINAL APPEAL)\s*(?:NO\.?)?\s*\d+(?:[-/]\d+)*(?:\s*OF\s*\d{4})?\b"
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"✅ Regex pattern matching works: found {len(matches)} matches")
    except Exception as e:
        print(f"❌ Regex processing failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🏛️ AI Legal Document Analyzer - System Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Model Loading Test", test_models),
        ("Qdrant Connection Test", test_qdrant_connection),
        ("Google AI Test", test_google_ai),
        ("App Modules Test", test_app_modules),
        ("Basic Functionality Test", test_basic_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("\nTo start the application, run:")
        print("   streamlit run app.py")
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the application.")
        print("\nCommon fixes:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Download SpaCy model: python -m spacy download en_core_web_sm")
        print("- Start Qdrant: docker run -p 6333:6333 qdrant/qdrant")
        print("- Set Google AI API key in environment or rag.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
