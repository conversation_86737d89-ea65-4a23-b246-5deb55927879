import re
from typing import List, Optional, Callable


class RecursiveCharacterTextSplitter:
    
    def __init__(
        self,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        length_function: Callable[[str], int] = len,
        separators: Optional[List[str]] = None,
        keep_separator: bool = True
    ):
        self._chunk_size = chunk_size
        self._chunk_overlap = chunk_overlap
        self._length_function = length_function
        self._keep_separator = keep_separator
        
        # Default separators in order of preference
        if separators is None:
            self._separators = [
                "\n\n",  
                "\n",    
                " ",     
                ""       
            ]
        else:
            self._separators = separators
    
    def split_text(self, text: str) -> List[str]:
        
        return self._split_text(text, self._separators)
    
    def _split_text(self, text: str, separators: List[str]) -> List[str]:
        
        final_chunks = []
        
        separator = separators[0] if separators else ""
        new_separators = separators[1:] if len(separators) > 1 else []
        
        if separator == "":
            splits = list(text)
        else:
            if separator in text:
                splits = self._split_on_separator(text, separator)
            else:
                splits = [text]
        
        good_splits = []
        for split in splits:
            if self._length_function(split) < self._chunk_size:
                good_splits.append(split)
            else:
                if new_separators:
                    good_splits.extend(self._split_text(split, new_separators))
                else:
                    good_splits.extend(self._force_split(split))
        
        return self._merge_splits(good_splits, separator)
    
    def _split_on_separator(self, text: str, separator: str) -> List[str]:
        if self._keep_separator and separator != "":
            pattern = f"({re.escape(separator)})"
            splits = re.split(pattern, text)
            
            result = []
            for i in range(0, len(splits), 2):
                chunk = splits[i]
                if i + 1 < len(splits):
                    chunk += splits[i + 1]
                if chunk:  
                    result.append(chunk)
            return result
        else:
            return [s for s in text.split(separator) if s]
    
    def _force_split(self, text: str) -> List[str]:
        chunks = []
        for i in range(0, len(text), self._chunk_size):
            chunks.append(text[i:i + self._chunk_size])
        return chunks
    
    def _merge_splits(self, splits: List[str], separator: str) -> List[str]:
        if not splits:
            return []
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for split in splits:
            split_length = self._length_function(split)
            
            if (current_length + split_length > self._chunk_size and 
                current_chunk and 
                current_length > 0):
                
                chunk_text = self._join_splits(current_chunk, separator)
                chunks.append(chunk_text)
                
                current_chunk, current_length = self._create_overlapping_chunk(
                    chunks[-1], split, separator
                )
            else:
                current_chunk.append(split)
                current_length += split_length
                if separator and len(current_chunk) > 1:
                    current_length += self._length_function(separator)
        
        if current_chunk:
            chunk_text = self._join_splits(current_chunk, separator)
            chunks.append(chunk_text)
        
        return chunks
    
    def _join_splits(self, splits: List[str], separator: str) -> str:
        if self._keep_separator or separator == "":
            return "".join(splits)
        else:
            return separator.join(splits)
    
    def _create_overlapping_chunk(self, prev_chunk: str, new_split: str, separator: str) -> tuple:
        if self._chunk_overlap <= 0:
            return [new_split], self._length_function(new_split)
        
        overlap_text = prev_chunk[-self._chunk_overlap:]
        
        if separator and separator in overlap_text:
            last_sep_idx = overlap_text.rfind(separator)
            if last_sep_idx > 0:
                overlap_text = overlap_text[last_sep_idx:]
        
        if self._keep_separator or separator == "":
            new_chunk = [overlap_text + new_split]
        else:
            new_chunk = [overlap_text, new_split]
        
        new_length = self._length_function(overlap_text) + self._length_function(new_split)
        if not (self._keep_separator or separator == "") and separator:
            new_length += self._length_function(separator)
        
        return new_chunk, new_length


if __name__ == "__main__":
    
    sample_text = """
    This is the first paragraph. It contains multiple sentences that we want to keep together if possible.
    
    This is the second paragraph. It also has several sentences. We want to demonstrate how the recursive splitter works.
    
    This is a very long paragraph that might need to be split even though it's a single paragraph. It contains many sentences and lots of words that will definitely exceed our chunk size limit when we set it to something small for testing purposes. The splitter should handle this gracefully by moving to the next level of separators.
    
    Final paragraph for testing.
    """
    

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=150,
        chunk_overlap=20
    )
    
    chunks = splitter.split_text(sample_text)
    
    print(f"Original text length: {len(sample_text)}")
    print(f"Number of chunks: {len(chunks)}")
    print("\nChunks:")
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1} (length: {len(chunk)}):")
        print(repr(chunk))