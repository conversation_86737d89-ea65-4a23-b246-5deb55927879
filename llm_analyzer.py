import json
import re
import os

def _fallback_analysis(text: str):
    """Fallback analysis using regex patterns when LLM is not available"""
    entities = {}
    clauses = {}

    # Basic regex patterns for entity extraction
    patterns = {
        "Case_Numbers": r"\b(?:CIVIL APPEAL|CRIMINAL APPEAL|WRIT PETITION|PIL)\s*(?:NO\.?)?\s*\d+(?:[-/]\d+)*(?:\s*OF\s*\d{4})?\b",
        "Court_Names": r"\b(?:SUPREME COURT|HIGH COURT|DISTRICT COURT)\s+(?:OF\s+)?[\w\s]+\b",
        "Dates": r"\b\d{1,2}[./]\d{1,2}[./]\d{2,4}\b|\b\d{1,2}(?:st|nd|rd|th)?\s+(?:JANUARY|FEBRUARY|MARCH|APRIL|MAY|JUNE|JULY|AUGUST|SEPTEMBER|OCTOBER|NOVEMBER|DECEMBER)\s*,?\s+\d{4}\b",
        "Money_Amounts": r"\b(?:Rs\.?|INR|USD|\$|₹)\s*\d+(?:,\d{2,3})*(?:\.\d{2})?\b",
        "Parties": r"\b[A-Z][A-Z\s&]+(?:AUTHORITY|CORPORATION|LIMITED|LTD|COMPANY|ASSOCIATION)\b",
        "Judges": r"\b(?:JUSTICE|HON'BLE|HONOURABLE)\s+[A-Z][A-Z\s\.]+\b"
    }

    for category, pattern in patterns.items():
        matches = re.findall(pattern, text, re.IGNORECASE)
        entities[category] = list(set(matches))  # Remove duplicates

    # Basic clause detection
    clause_patterns = {
        "Governing_Law_and_Jurisdiction": r"(?:jurisdiction|governing law|competent court).*?(?:\.|;|$)",
        "Arbitration": r"(?:arbitration|arbitrator|dispute resolution).*?(?:\.|;|$)",
        "Confidentiality": r"(?:confidential|non-disclosure|proprietary).*?(?:\.|;|$)",
        "Termination": r"(?:termination|terminate|dissolution).*?(?:\.|;|$)",
        "Payment_Terms": r"(?:payment|remuneration|consideration|fees?).*?(?:\.|;|$)"
    }

    for category, pattern in clause_patterns.items():
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        clauses[category] = matches[:3]  # Limit to 3 matches

    return {
        "named_entities": entities,
        "legal_clauses": clauses
    }

def analyze_text_with_llm(text: str):
    """
    Analyzes text using a Large Language Model to extract named entities and legal clauses.
    Falls back to regex-based analysis if LLM is not available.
    """
    try:
        import google.generativeai as genai
        api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyD5zkhwgtfpfMeMqF4Jz2LRd7Loxa59ZDM')
        genai.configure(api_key=api_key)

        prompt = f"""
        You are an expert legal assistant. Analyze the following legal document text and extract information in JSON format.

        Extract:
        1. "named_entities" with categories: Case_Numbers, Court_Names, Parties, Judges, Dates, Statutes, Money_Amounts
        2. "legal_clauses" with categories: Governing_Law_and_Jurisdiction, Arbitration, Confidentiality, Termination, Payment_Terms

        Document Text (first 5000 chars):
        {text[:5000]}

        Respond with valid JSON only.
        """

        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(prompt)

        # Try to extract JSON from response
        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            parsed_json = json.loads(json_str)
            return parsed_json
        else:
            raise ValueError("No valid JSON found in response")

    except Exception as e:
        print(f"LLM analysis failed: {e}")
        print("Using fallback regex-based analysis...")
        return _fallback_analysis(text)