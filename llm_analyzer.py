import google.generativeai as genai
import json
import re
import os

def analyze_text_with_llm(text: str):
    """
    Analyzes text using a Large Language Model to extract named entities and legal clauses.
    """
    # NOTE: The API key is now expected to be set as an environment variable 'GOOGLE_API_KEY'.
    # This is a more secure practice than hardcoding keys in the source code.
    try:
        client = genai.configure(api_key=os.environ['API_KEY']) # The client will automatically find the GOOGLE_API_KEY environment variable.
    except Exception as e:
        print(f"Error initializing Google AI Client: {e}")
        print("Please ensure the GOOGLE_API_KEY environment variable is set correctly.")
        return {
            "named_entities": {"error": "Google AI Client initialization failed."},
            "legal_clauses": {"error": "Google AI Client initialization failed."}
        }


    prompt = f"""
    You are an expert legal assistant. Analyze the following legal document text and extract the following information in a structured JSON format.

    The JSON object should have two main keys: "named_entities" and "legal_clauses".

    1.  For "named_entities", extract the following categories as lists of strings:
        -   `Case_Numbers`: e.g., "CIVIL APPEAL NO. 7527-7528 OF 2023"
        -   `Court_Names`: e.g., "SUPREME COURT OF INDIA", "Delhi High Court"
        -   `Parties`: e.g., "DELHI DEVELOPMENT AUTHORITY", "YOGENDRA RATHI"
        -   `Judges`: e.g., "JUSTICE RAJESH BINDAL"
        -   `Dates`: e.g., "JUNE 02, 2023"
        -   `Statutes`: e.g., "Land Acquisition Act, 1894"
        -   `Money_Amounts`: e.g., "Rs. 10,50,000/-"

    2.  For "legal_clauses", extract the full text of clauses related to the following categories as a list of strings:
        -   `Governing_Law_and_Jurisdiction`
        -   `Arbitration`
        -   `Confidentiality`
        -   `Termination`
        -   `Limitation_of_Liability`
        -   `Intellectual_Property`
        -   `Force_Majeure`
        -   `Indemnification`
        -   `Payment_Terms`

    Provide the output as a single, valid JSON object. Do not include any text or explanations outside of the JSON object.

    Document Text:
    ---
    {text[:10000]}
    ---
    """
    try:
        response = client.generate_content(model='gemini-2.5-flash', contents=prompt)
        
        # Find the JSON block in the response
        json_match = re.search(r'```json\n(.*?)\n```', response.text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            json_str = response.text
        
        parsed_json = json.loads(json_str)
        return parsed_json
    except Exception as e:
        print(f"Error during LLM analysis: {e}")
        return {
            "named_entities": {"error": f"Failed to extract entities from LLM response: {e}"},
            "legal_clauses": {"error": f"Failed to extract clauses from LLM response: {e}"}
     }