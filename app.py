import streamlit as st
import torch
from transformers import T5ForConditionalGeneration, T5Tokenizer
import PyPDF2
import docx
from io import BytesIO
import time
import spacy
import re
from phrasetxt import embedtxt
from rag import ai_response
from qdrant_client import QdrantClient

st.set_page_config(
    page_title="Legal Document Intelligence",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded",
)


HF_REPO_ID = "ramnotfound/legal-summarizer-t5-billsum"

@st.cache_resource
def load_summarizer_model():
    try:
        tokenizer = T5Tokenizer.from_pretrained(HF_REPO_ID)
        model = T5ForConditionalGeneration.from_pretrained(HF_REPO_ID)
        return model, tokenizer
    except Exception as e:
        st.error(f"Error loading summarizer model: {e}")
        return None, None

@st.cache_resource
def load_spacy_model():
    try:
        return spacy.load("en_core_web_sm")
    except OSError:
        st.error("SpaCy model 'en_core_web_sm' not found. Please run 'python -m spacy download en_core_web_sm' in your terminal.")
        return None

summarizer_model, tokenizer = load_summarizer_model()
nlp = load_spacy_model()
qdrant_client = QdrantClient(host="localhost", port=6333)

def extract_text_from_file(uploaded_file):
    text = ""
    try:
        file_stream = BytesIO(uploaded_file.getvalue())
        if uploaded_file.type == "application/pdf":
            pdf_reader = PyPDF2.PdfReader(file_stream)
            for page in pdf_reader.pages:
                text += (page.extract_text() or "") + "\n"
        elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            doc = docx.Document(file_stream)
            for para in doc.paragraphs:
                text += para.text + "\n"
    except Exception as e:
        st.error(f"Error processing file '{uploaded_file.name}': {e}")
        return None
    return text

def generate_summary(document_text, model, tokenizer):
    if not document_text:
        return "Could not extract text. Nothing to summarize."
    with st.spinner("Summarizing the document..."):
        try:
            input_text = "summarize: " + document_text
            inputs = tokenizer(input_text, return_tensors="pt", max_length=1024, truncation=True)
            with torch.no_grad():
                summary_ids = model.generate(inputs["input_ids"], max_length=256, num_beams=4, early_stopping=True)
            return tokenizer.decode(summary_ids[0], skip_special_tokens=True)
        except Exception as e:
            st.error(f"An error occurred during summarization: {e}")
            return "Failed to generate summary."

def find_named_entities(text):
    if not nlp or not text:
        return {}
    doc = nlp(text)
    grouped_entities = {}
    for ent in doc.ents:
        label = ent.label_
        if label not in grouped_entities:
            grouped_entities[label] = []
        if ent.text not in grouped_entities[label]:
            grouped_entities[label].append(ent.text)
    # Sort entities within each category
    for label in grouped_entities:
        grouped_entities[label].sort()
    return grouped_entities

def find_clauses(text):
    if not text:
        return {}
    
    clause_patterns = {
        "Governing Law": r"governing\s+law.*?(?:\.|$)",
        "Limitation of Liability": r"limitation\s+of\s+liability.*?(?:\.|$)",
        "Confidentiality": r"confidentiality.*?(?:\.|$)",
        "Termination": r"termination\s+for\s+cause.*?(?:\.|$)",
        "Indemnification": r"indemnification.*?(?:\.|$)"
    }
    
    found_clauses = {}
    for clause, pattern in clause_patterns.items():
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            found_clauses[clause] = [match.strip() for match in matches]
    return found_clauses

def stream_response(text):
    for word in text.split():
        yield word + " "
        time.sleep(0.05)

with st.sidebar:
    st.header("Upload Document")
    st.markdown("Upload a document to summarize, find entities, and detect clauses.")
    uploaded_file = st.file_uploader("Choose a file", type=["pdf", "docx"], label_visibility="collapsed")
    
    if st.button("Delete Analysis"):
        if "collection_name" in st.session_state:
            try:
                qdrant_client.delete_collection(collection_name=st.session_state.collection_name)
                st.success(f"Collection '{st.session_state.collection_name}' deleted.")
            except Exception as e:
                st.error(f"Error deleting collection: {e}")
        
        # Clear session state
        for key in list(st.session_state.keys()):
            del st.session_state[key]
        st.rerun()

    st.markdown("---")
    st.markdown(f"**Summarizer:** `{HF_REPO_ID}`")
    st.markdown(f"**NER Model:** `en_core_web_sm`")
    if summarizer_model is None or nlp is None:
        st.error("A required model could not be loaded.")
    else:
        st.success("All models loaded successfully!")

st.title("⚖️ Legal Document Intelligence")

if "messages" not in st.session_state:
    st.session_state.messages = [{"role": "ai", "content": "Hello! Please upload a document to begin analysis."}]
if "collection_name" not in st.session_state:
    st.session_state.collection_name = None
if "last_processed_file" not in st.session_state:
    st.session_state.last_processed_file = None

# Process uploaded file first
if uploaded_file is not None and st.session_state.last_processed_file != uploaded_file.name:
    st.session_state.document_text = extract_text_from_file(uploaded_file)
    st.session_state.last_processed_file = uploaded_file.name
    # Clear previous analysis results and chat
    st.session_state.messages = []
    st.session_state.entities = []
    st.session_state.clauses = {}
    st.session_state.collection_name = None # Reset collection name
    
    if st.session_state.document_text:
        # Embed the document for RAG
        with st.spinner("Processing and embedding document for RAG..."):
            try:
                st.session_state.collection_name = embedtxt(st.session_state.document_text, uploaded_file.name)
            except Exception as e:
                st.error(f"Failed to embed document: {e}")

        if st.session_state.collection_name:
            summary = generate_summary(st.session_state.document_text, summarizer_model, tokenizer)
            st.session_state.entities = find_named_entities(st.session_state.document_text)
            st.session_state.clauses = find_clauses(st.session_state.document_text)
            st.session_state.messages.append({"role": "ai", "content": f"**Summary of `{uploaded_file.name}`:**\n\n{summary}"})
    else:
        error_message = f"Could not extract text from `{uploaded_file.name}`."
        st.session_state.messages.append({"role": "ai", "content": error_message})

# Display analysis results if available
if "entities" in st.session_state and st.session_state.entities:
    with st.expander("Named Entities Found", expanded=True):
        for label, entities in st.session_state.entities.items():
            st.markdown(f"**{label}** ({len(entities)} found)")
            for entity in entities:
                st.markdown(f"- {entity}")

if "clauses" in st.session_state and st.session_state.clauses:
    with st.expander("Potential Clauses Detected", expanded=True):
        for clause, matches in st.session_state.clauses.items():
            st.markdown(f"**{clause}**")
            for match in matches:
                st.info(match)

st.markdown("---")
st.header("Chat With Your Document")

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Chat input box
if prompt := st.chat_input("Ask a question..."):
    with st.chat_message("human"):
        st.markdown(prompt)
    st.session_state.messages.append({"role": "human", "content": prompt})

    with st.chat_message("ai"):
        if st.session_state.collection_name:
            full_response = st.write_stream(ai_response(prompt, st.session_state.collection_name))
        else:
            response_text = "Please upload a document first to chat with it."
            full_response = st.write_stream(stream_response(response_text))
    st.session_state.messages.append({"role": "ai", "content": full_response})
