import streamlit as st
import torch
from transformers import T5ForConditionalGeneration, T5Tokenizer, pipeline
import PyPDF2
import docx
from io import BytesIO
import time
import spacy
import re
import pandas as pd
import plotly.express as px
from collections import Counter
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from wordcloud import WordCloud
import matplotlib.pyplot as plt
from phrasetxt import embedtxt
from rag import ai_response, ai_image_response
from llm_analyzer import analyze_text_with_llm
from qdrant_client import QdrantClient

# --- (Rest of the initial imports and setup from your original file) ---

# Download required NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

st.set_page_config(
    page_title="AI Legal Document Analyzer",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded",
)

# --- (Custom CSS from your original file) ---
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2a5298;
        margin: 0.5rem 0;
    }
    .entity-tag {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        margin: 0.1rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .person-tag { background-color: #e3f2fd; color: #1976d2; }
    .org-tag { background-color: #f3e5f5; color: #7b1fa2; }
    .location-tag { background-color: #e8f5e8; color: #388e3c; }
    .date-tag { background-color: #fff3e0; color: #f57c00; }
    .money-tag { background-color: #fce4ec; color: #c2185b; }
    .law-tag { background-color: #e0f2f1; color: #00695c; }
</style>
""", unsafe_allow_html=True)

HF_REPO_ID = "ramnotfound/legal-summarizer-t5-billsum"

@st.cache_resource
def load_models():
    # --- (load_models function from your original file) ---
    models = {}
    try:
        # Load summarizer
        tokenizer = T5Tokenizer.from_pretrained(HF_REPO_ID)
        model = T5ForConditionalGeneration.from_pretrained(HF_REPO_ID)
        models['summarizer'] = (model, tokenizer)
        # Load SpaCy model
        models['nlp'] = spacy.load("en_core_web_sm")
        # Load sentiment analyzer
        models['sentiment'] = SentimentIntensityAnalyzer()
        # Load NER pipeline
        models['ner_pipeline'] = pipeline("ner", model="dbmdz/bert-base-cased-finetuned-conll03-english", aggregation_strategy="simple")
        return models
    except Exception as e:
        st.error(f"Error loading models: {e}")
        return {}


models = load_models()
qdrant_client = QdrantClient(host="localhost", port=6333)

# --- (All helper functions from your original file: extract_text_from_file, generate_summary, advanced_entity_extraction, etc.) ---
def extract_text_from_file(uploaded_file):
    """Extract text from uploaded PDF or DOCX files"""
    text = ""
    try:
        file_stream = BytesIO(uploaded_file.getvalue())
        if uploaded_file.type == "application/pdf":
            pdf_reader = PyPDF2.PdfReader(file_stream)
            for page in pdf_reader.pages:
                text += (page.extract_text() or "") + "\n"
        elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            doc = docx.Document(file_stream)
            for para in doc.paragraphs:
                text += para.text + "\n"
    except Exception as e:
        st.error(f"Error processing file '{uploaded_file.name}': {e}")
        return None
    return text

def generate_summary(document_text, model, tokenizer):
    """Generate document summary using T5 model"""
    if not document_text:
        return "Could not extract text. Nothing to summarize."
    with st.spinner("Summarizing the document..."):
        try:
            input_text = "summarize: " + document_text[:1000]  # Limit input length
            inputs = tokenizer(input_text, return_tensors="pt", max_length=1024, truncation=True)
            with torch.no_grad():
                summary_ids = model.generate(inputs["input_ids"], max_length=256, num_beams=4, early_stopping=True)
            return tokenizer.decode(summary_ids[0], skip_special_tokens=True)
        except Exception as e:
            st.error(f"An error occurred during summarization: {e}")
            return "Failed to generate summary."

def advanced_entity_extraction(text):
    if not text: return {}
    entities = {}
    if 'nlp' in models:
        doc = models['nlp'](text)
        for ent in doc.ents:
            label = ent.label_
            if label not in entities: entities[label] = set()
            entities[label].add(ent.text.strip())
    if 'ner_pipeline' in models:
        try:
            ner_results = models['ner_pipeline'](text[:5000])
            for entity in ner_results:
                label = entity['entity_group']
                if label not in entities: entities[label] = set()
                entities[label].add(entity['word'].strip())
        except: pass
    legal_patterns = {
        "Case_Numbers": r"\b(?:Civil Appeal|Criminal Appeal|Writ Petition|PIL)\s*(?:No\.?)?\s*(\d+(?:[-/]\d+)*)\b",
        "Court_Names": r"\b(?:Supreme Court|High Court|District Court|Tribunal|Commission)\s+of\s+\w+\b",
    }
    for pattern_name, pattern in legal_patterns.items():
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            if pattern_name not in entities: entities[pattern_name] = set()
            entities[pattern_name].update(matches)
    for key in entities:
        entities[key] = sorted(list(entities[key]))
    return entities

def find_legal_clauses(text):
    if not text: return {}
    clause_patterns = {
        "Jurisdiction": r"(?:jurisdiction|competent court|courts? of).*?(?:\.|;|$)",
        "Governing Law": r"(?:governing law|governed by|applicable law).*?(?:\.|;|$)",
    }
    found_clauses = {}
    for clause, pattern in clause_patterns.items():
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            found_clauses[clause] = [match.strip() for match in matches[:3]]
    return found_clauses
def analyze_sentiment(text):
    if not text or 'sentiment' not in models: return {}
    try:
        sentences = sent_tokenize(text)
        sentiments = [models['sentiment'].polarity_scores(s) for s in sentences[:50]]
        avg_sentiment = {
            'compound': sum(s['compound'] for s in sentiments) / len(sentiments),
            'positive': sum(s['pos'] for s in sentiments) / len(sentiments),
            'negative': sum(s['neg'] for s in sentiments) / len(sentiments),
            'neutral': sum(s['neu'] for s in sentiments) / len(sentiments)
        }
        return avg_sentiment
    except: return {}

def generate_word_cloud(text):
    if not text: return None
    try:
        stop_words = set(stopwords.words('english'))
        words = [word for word in word_tokenize(text.lower()) if word.isalpha() and word not in stop_words and len(word) > 3]
        if not words: return None
        wordcloud = WordCloud(width=800, height=400, background_color='white').generate(' '.join(words))
        fig, ax = plt.subplots(figsize=(10, 5))
        ax.imshow(wordcloud, interpolation='bilinear')
        ax.axis('off')
        return fig
    except: return None

def stream_response(text):
    """Stream response word by word"""
    for word in text.split():
        yield word + " "
        time.sleep(0.05)
# Sidebar
with st.sidebar:
    st.markdown("""
    <div class="main-header" style="padding: 1.5rem;">
        <h2>⚖️ AI Legal Analyzer</h2>
    </div>
    """, unsafe_allow_html=True)

    st.header("📄 Upload Document")
    uploaded_file = st.file_uploader("Choose a file", type=["pdf", "docx"], label_visibility="collapsed")

    st.header("🔧 Analysis Options")
    analysis_options = st.multiselect(
        "Select analysis types:",
        ["Summary", "Named Entities", "Legal Clauses", "Sentiment Analysis", "Word Cloud", "LLM Analysis"],
        default=["Summary", "Named Entities", "Legal Clauses", "LLM Analysis"]
    )
    
    # ... (rest of sidebar: expander, clear button, model status)

# Main header
st.markdown("""
<div class="main-header">
    <h1>⚖️ AI Legal Document Analyzer</h1>
    <p>Comprehensive Legal Document Intelligence Platform</p>
</div>
""", unsafe_allow_html=True)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = [{"role": "ai", "content": "👋 Welcome! Upload a legal document or an image to begin."}]
if "collection_name" not in st.session_state: st.session_state.collection_name = None
if "last_processed_file" not in st.session_state: st.session_state.last_processed_file = None
if "analysis_results" not in st.session_state: st.session_state.analysis_results = {}

# Process uploaded file
if uploaded_file is not None and st.session_state.last_processed_file != uploaded_file.name:
    with st.spinner("🔄 Processing document..."):
        st.session_state.document_text = extract_text_from_file(uploaded_file)
        st.session_state.last_processed_file = uploaded_file.name
        st.session_state.messages = []
        st.session_state.analysis_results = {}
        st.session_state.collection_name = None

        if st.session_state.document_text:
            try:
                st.session_state.collection_name = embedtxt(st.session_state.document_text, uploaded_file.name)
            except Exception as e:
                st.error(f"Failed to embed document: {e}")

            if st.session_state.collection_name:
                results = {}
                if "Summary" in analysis_options and 'summarizer' in models:
                    model, tokenizer = models['summarizer']
                    results['summary'] = generate_summary(st.session_state.document_text, model, tokenizer)
                if "Named Entities" in analysis_options:
                    results['entities'] = advanced_entity_extraction(st.session_state.document_text)
                if "Legal Clauses" in analysis_options:
                    results['clauses'] = find_legal_clauses(st.session_state.document_text)
                if "Sentiment Analysis" in analysis_options:
                    results['sentiment'] = analyze_sentiment(st.session_state.document_text)
                if "Word Cloud" in analysis_options:
                    results['wordcloud'] = generate_word_cloud(st.session_state.document_text)
                if "LLM Analysis" in analysis_options:
                    with st.spinner("🤖 Performing LLM Analysis..."):
                        results['llm_analysis'] = analyze_text_with_llm(st.session_state.document_text)
                
                st.session_state.analysis_results = results
                if results.get('summary'):
                    st.session_state.messages.append({"role": "ai", "content": f"📄 **Analysis Complete for `{uploaded_file.name}`**\n\n**Summary:**\n{results['summary']}"})
        else:
            st.session_state.messages.append({"role": "ai", "content": f"❌ Could not extract text from `{uploaded_file.name}`."})

# Display analysis results
if "analysis_results" in st.session_state and st.session_state.analysis_results:
    results = st.session_state.analysis_results
    tab_names = []
    if 'entities' in results: tab_names.append("🏷️ Named Entities")
    if 'clauses' in results: tab_names.append("📋 Legal Clauses")
    if 'llm_analysis' in results: tab_names.append("🤖 LLM Analysis")
    if 'sentiment' in results: tab_names.append("😊 Sentiment")
    if 'wordcloud' in results: tab_names.append("☁️ Word Cloud")
    
    if tab_names:
        tabs = st.tabs(tab_names)
        tab_idx = 0

        if 'entities' in results:
            with tabs[tab_idx]:
                # --- (Your original Named Entities tab content) ---
                pass
            tab_idx += 1
        
        if 'clauses' in results:
            with tabs[tab_idx]:
                # --- (Your original Legal Clauses tab content) ---
                pass
            tab_idx += 1

        if 'llm_analysis' in results:
            with tabs[tab_idx]:
                st.header("🤖 LLM-Powered Analysis")
                llm_results = results['llm_analysis']

                st.subheader("Named Entity Recognition (via LLM)")
                if 'named_entities' in llm_results and isinstance(llm_results['named_entities'], dict) and 'error' not in llm_results['named_entities']:
                    for entity_type, entities in llm_results['named_entities'].items():
                        with st.expander(f"{entity_type.replace('_', ' ').title()} ({len(entities)} found)", expanded=True):
                            if entities:
                                for ent in entities:
                                    st.markdown(f"- {ent}")
                            else:
                                st.markdown("_None found._")
                else:
                    st.error("Could not extract named entities using the LLM.")
                    st.write(llm_results.get('named_entities', {}).get('error', ''))

                st.subheader("Legal Clause Extraction (via LLM)")
                if 'legal_clauses' in llm_results and isinstance(llm_results['legal_clauses'], dict) and 'error' not in llm_results['legal_clauses']:
                    for clause_type, clauses in llm_results['legal_clauses'].items():
                        with st.expander(f"{clause_type.replace('_', ' ').title()}", expanded=True):
                            if clauses:
                                for clause in clauses:
                                    st.info(clause)
                            else:
                                st.markdown("_None found._")
                else:
                    st.error("Could not extract legal clauses using the LLM.")
                    st.write(llm_results.get('legal_clauses', {}).get('error', ''))
            tab_idx += 1
        
        # --- (Your other tabs: Sentiment, Word Cloud...) ---

st.markdown("---")
st.header("💬 Chat With Your Document or Image")

# Image uploader for chat
chat_image = st.file_uploader("Upload an image to discuss", type=["png", "jpg", "jpeg"], key="chat_image_uploader")

if chat_image:
    st.image(chat_image, caption="Image for chat analysis", width=200)

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Chat input
if prompt := st.chat_input("Ask a question..."):
    st.session_state.messages.append({"role": "human", "content": prompt})
    with st.chat_message("human"):
        st.markdown(prompt)

    with st.chat_message("ai"):
        if chat_image:
            image_bytes = chat_image.getvalue()
            full_response = st.write_stream(ai_image_response(prompt, image_bytes))
        elif st.session_state.collection_name:
            full_response = st.write_stream(ai_response(prompt, st.session_state.collection_name))
        else:
            response_text = "Please upload a document or an image to begin the chat."
            full_response = st.write_stream(stream_response(response_text))
    
    st.session_state.messages.append({"role": "ai", "content": str(full_response)})