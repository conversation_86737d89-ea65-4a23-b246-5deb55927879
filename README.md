# 🏛️ AI Legal Document Analyzer

A comprehensive AI-powered legal document analysis platform built with Streamlit, featuring advanced NLP capabilities, RAG (Retrieval-Augmented Generation), and interactive visualizations.

## ✨ Features

### 🔍 Advanced Named Entity Recognition
- **Multi-Model Approach**: Combines SpaCy and transformer-based models for better accuracy
- **Legal-Specific Patterns**: Custom regex patterns for legal entities like:
  - Case numbers and citations
  - Court names and jurisdictions
  - Legal statutes and sections
  - Dates and monetary amounts
- **Interactive Visualization**: Color-coded entity tags with confidence scores

### 📋 Legal Clause Detection
- **Comprehensive Clause Library**: Detects 10+ types of legal clauses:
  - Jurisdiction and Governing Law
  - Limitation of Liability
  - Confidentiality and Non-disclosure
  - Termination and Force Majeure
  - Arbitration and Dispute Resolution
  - Intellectual Property
  - Payment Terms
- **Context-Aware Extraction**: Provides full clause text with context

### 🤖 AI-Powered Document Chat
- **RAG Implementation**: Chat with your documents using vector embeddings
- **Contextual Responses**: AI responses based on document content
- **Streaming Interface**: Real-time response generation

### 📊 Document Analytics
- **Sentiment Analysis**: VADER sentiment analysis for document tone
- **Document Structure**: Automatic section detection and hierarchy analysis
- **Word Cloud Generation**: Visual representation of key terms
- **Statistical Insights**: Document metrics and entity distribution

### 🎨 Enhanced UI/UX
- **Modern Design**: Clean, professional interface with custom CSS
- **Interactive Tabs**: Organized analysis results in tabbed interface
- **Real-time Metrics**: Live statistics and progress indicators
- **Responsive Layout**: Works on desktop and mobile devices

## 🚀 Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd AI-Legal-Doc-Analyzer
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Download SpaCy model**:
```bash
python -m spacy download en_core_web_sm
```

4. **Start Qdrant vector database**:
```bash
docker run -p 6333:6333 qdrant/qdrant
```

5. **Set up Google AI API**:
   - Get API key from Google AI Studio
   - Update the API key in `rag.py`

## 🏃‍♂️ Usage

1. **Start the application**:
```bash
streamlit run app.py
```

2. **Upload a document**:
   - Supports PDF and DOCX formats
   - Automatic text extraction and processing

3. **Select analysis options**:
   - Choose from multiple analysis types
   - Customize analysis parameters

4. **Explore results**:
   - Navigate through tabbed interface
   - Interact with visualizations
   - Chat with your document

## 🛠️ Technical Architecture

### Core Components
- **Frontend**: Streamlit with custom CSS styling
- **NLP Models**: 
  - SpaCy `en_core_web_sm`
  - Hugging Face transformers
  - T5 for summarization
  - BERT for NER
- **Vector Database**: Qdrant for document embeddings
- **AI Integration**: Google Gemini for chat responses

### Data Flow
1. Document upload and text extraction
2. Text preprocessing and chunking
3. Vector embedding generation
4. Multi-model entity extraction
5. Legal clause pattern matching
6. Sentiment and structure analysis
7. Interactive visualization rendering

## 📁 Project Structure

```
AI-Legal-Doc-Analyzer/
├── app.py                 # Main Streamlit application
├── rag.py                 # RAG implementation
├── phrasetxt.py           # Text embedding utilities
├── splitter.py            # Text chunking utilities
├── requirements.txt       # Python dependencies
└── README.md             # Project documentation
```

## 🔧 Configuration

### Analysis Options
- **Summary Length**: Adjustable summary length (100-500 tokens)
- **Entity Confidence**: Confidence threshold for entity extraction
- **Context Inclusion**: Include surrounding context for entities

### Model Settings
- **Summarizer**: T5-based legal document summarizer
- **NER Pipeline**: BERT-based named entity recognition
- **Embeddings**: FastEmbed for vector generation

## 🎯 Use Cases

### Legal Professionals
- **Contract Review**: Automated clause detection and analysis
- **Due Diligence**: Quick document summarization and entity extraction
- **Case Research**: Intelligent document search and retrieval

### Researchers
- **Document Analysis**: Comprehensive text analytics
- **Pattern Recognition**: Legal pattern and trend identification
- **Data Extraction**: Structured data from unstructured documents

### Students
- **Legal Education**: Understanding document structure and clauses
- **Research Projects**: Automated analysis for academic work
- **Case Studies**: Interactive exploration of legal documents

## 🚀 Future Enhancements

- [ ] Multi-language support
- [ ] Advanced legal reasoning
- [ ] Document comparison features
- [ ] Export functionality
- [ ] API integration
- [ ] Batch processing
- [ ] Custom model training

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Hugging Face for transformer models
- SpaCy for NLP capabilities
- Streamlit for the web framework
- Qdrant for vector database
- Google AI for language models
