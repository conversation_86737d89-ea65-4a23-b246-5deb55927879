import streamlit as st

import torch

from transformers import T5ForConditionalGeneration, T5Tokenizer, pipeline

import PyPDF2

import docx

from io import BytesIO

import time

import spacy

import re

import pandas as pd

import plotly.express as px

import plotly.graph_objects as go

from collections import Counter

import nltk

from nltk.sentiment import SentimentIntensityAnalyzer

from nltk.corpus import stopwords

from nltk.tokenize import word_tokenize, sent_tokenize

from wordcloud import WordCloud

import matplotlib.pyplot as plt

from phrasetxt import embedtxt

from rag import ai_response

from qdrant_client import QdrantClient

import json

from datetime import datetime

import base64



# Download required NLTK data

try:

    nltk.download('vader_lexicon', quiet=True)

    nltk.download('punkt', quiet=True)

    nltk.download('stopwords', quiet=True)

except:

    pass



st.set_page_config(

    page_title="AI Legal Document Analyzer",

    page_icon="⚖️",

    layout="wide",

    initial_sidebar_state="expanded",

)



# Custom CSS for better UI

st.markdown("""

<style>

    .main-header {

        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);

        padding: 2rem;

        border-radius: 10px;

        margin-bottom: 2rem;

        color: white;

        text-align: center;

    }

    .metric-card {

        background: #f8f9fa;

        padding: 1rem;

        border-radius: 8px;

        border-left: 4px solid #2a5298;

        margin: 0.5rem 0;

    }

    .entity-tag {

        display: inline-block;

        padding: 0.2rem 0.5rem;

        margin: 0.1rem;

        border-radius: 15px;

        font-size: 0.8rem;

        font-weight: bold;

    }

    .person-tag { background-color: #e3f2fd; color: #1976d2; }

    .org-tag { background-color: #f3e5f5; color: #7b1fa2; }

    .location-tag { background-color: #e8f5e8; color: #388e3c; }

    .date-tag { background-color: #fff3e0; color: #f57c00; }

    .money-tag { background-color: #fce4ec; color: #c2185b; }

    .law-tag { background-color: #e0f2f1; color: #00695c; }

</style>

""", unsafe_allow_html=True)



HF_REPO_ID = "ramnotfound/legal-summarizer-t5-billsum"



@st.cache_resource

def load_models():

    models = {}

    try:

        # Load summarizer

        tokenizer = T5Tokenizer.from_pretrained(HF_REPO_ID)

        model = T5ForConditionalGeneration.from_pretrained(HF_REPO_ID)

        models['summarizer'] = (model, tokenizer)



        # Load SpaCy model

        models['nlp'] = spacy.load("en_core_web_sm")



        # Load sentiment analyzer

        models['sentiment'] = SentimentIntensityAnalyzer()



        # Load NER pipeline for better entity recognition

        models['ner_pipeline'] = pipeline("ner",

                                        model="dbmdz/bert-base-cased-finetuned-conll03-english",

                                        aggregation_strategy="simple")



        return models

    except Exception as e:

        st.error(f"Error loading models: {e}")

        return {}



models = load_models()

qdrant_client = QdrantClient(host="localhost", port=6333)



def extract_text_from_file(uploaded_file):

    """Extract text from uploaded PDF or DOCX files"""

    text = ""

    try:

        file_stream = BytesIO(uploaded_file.getvalue())

        if uploaded_file.type == "application/pdf":

            pdf_reader = PyPDF2.PdfReader(file_stream)

            for page in pdf_reader.pages:

                text += (page.extract_text() or "") + "\n"

        elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":

            doc = docx.Document(file_stream)

            for para in doc.paragraphs:

                text += para.text + "\n"

    except Exception as e:

        st.error(f"Error processing file '{uploaded_file.name}': {e}")

        return None

    return text



def generate_summary(document_text, model, tokenizer):

    """Generate document summary using T5 model"""

    if not document_text:

        return "Could not extract text. Nothing to summarize."

    with st.spinner("Summarizing the document..."):

        try:

            input_text = "summarize: " + document_text[:1000]  # Limit input length

            inputs = tokenizer(input_text, return_tensors="pt", max_length=1024, truncation=True)

            with torch.no_grad():

                summary_ids = model.generate(inputs["input_ids"], max_length=256, num_beams=4, early_stopping=True)

            return tokenizer.decode(summary_ids[0], skip_special_tokens=True)

        except Exception as e:

            st.error(f"An error occurred during summarization: {e}")

            return "Failed to generate summary."



def advanced_entity_extraction(text):

    """Enhanced entity extraction using multiple approaches"""

    if not text:

        return {}



    entities = {}



    # Use SpaCy for basic NER

    if 'nlp' in models:

        doc = models['nlp'](text)

        for ent in doc.ents:

            label = ent.label_

            if label not in entities:

                entities[label] = set()

            entities[label].add(ent.text.strip())



    # Use transformer-based NER for better accuracy

    if 'ner_pipeline' in models:

        try:

            ner_results = models['ner_pipeline'](text[:5000])  # Limit text length

            for entity in ner_results:

                label = entity['entity_group']

                if label not in entities:

                    entities[label] = set()

                entities[label].add(entity['word'].strip())

        except:

            pass



    # Legal-specific entity patterns

    legal_patterns = {

        "Case_Numbers": r"\b(?:Civil Appeal|Criminal Appeal|Writ Petition|PIL)\s*(?:No\.?)?\s*(\d+(?:[-/]\d+)*)\b",

        "Court_Names": r"\b(?:Supreme Court|High Court|District Court|Tribunal|Commission)\s+of\s+\w+\b",

        "Legal_Citations": r"\b\d{4}\s+\w+\s+\d+\b|\b\(\d{4}\)\s+\d+\s+\w+\s+\d+\b",

        "Statutes": r"\b(?:Section|Article|Rule|Regulation)\s+\d+(?:\([a-z]\))?(?:\s+of\s+.+?Act\b)?\b",

        "Dates": r"\b\d{1,2}[./]\d{1,2}[./]\d{2,4}\b|\b\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b",

        "Money_Amounts": r"\b(?:Rs\.?|INR|USD|\$)\s*\d+(?:,\d{3})*(?:\.\d{2})?\b|\b\d+(?:,\d{3})*\s*(?:crores?|lakhs?|thousands?)\b"

    }



    for pattern_name, pattern in legal_patterns.items():

        matches = re.findall(pattern, text, re.IGNORECASE)

        if matches:

            if pattern_name not in entities:

                entities[pattern_name] = set()

            entities[pattern_name].update(matches)



    # Convert sets to sorted lists

    for key in entities:

        entities[key] = sorted(list(entities[key]))



    return entities



def analyze_document_structure(text):

    """Analyze document structure and extract key sections"""

    if not text:

        return {}



    structure = {}



    # Find headings and sections

    heading_patterns = [

        r"^[A-Z\s]{3,}:?\s*$",  # All caps headings

        r"^\d+\.\s+[A-Z][^.]*$",  # Numbered sections

        r"^[IVX]+\.\s+[A-Z][^.]*$",  # Roman numeral sections

    ]



    lines = text.split('\n')

    sections = []

    current_section = None



    for i, line in enumerate(lines):

        line = line.strip()

        if not line:

            continue



        for pattern in heading_patterns:

            if re.match(pattern, line):

                if current_section:

                    sections.append(current_section)

                current_section = {

                    'title': line,

                    'start_line': i,

                    'content': []

                }

                break

        else:

            if current_section:

                current_section['content'].append(line)



    if current_section:

        sections.append(current_section)



    structure['sections'] = sections

    structure['total_sections'] = len(sections)

    structure['total_lines'] = len(lines)



    return structure



def find_legal_clauses(text):

    """Enhanced clause detection with legal-specific patterns"""

    if not text:

        return {}



    clause_patterns = {

        "Jurisdiction": r"(?:jurisdiction|competent court|courts? of).*?(?:\.|;|$)",

        "Governing Law": r"(?:governing law|governed by|applicable law).*?(?:\.|;|$)",

        "Limitation of Liability": r"(?:limitation of liability|liable|damages).*?(?:\.|;|$)",

        "Confidentiality": r"(?:confidential|non-disclosure|proprietary).*?(?:\.|;|$)",

        "Termination": r"(?:termination|terminate|dissolution).*?(?:\.|;|$)",

        "Indemnification": r"(?:indemnify|indemnification|hold harmless).*?(?:\.|;|$)",

        "Force Majeure": r"(?:force majeure|act of god|unforeseeable).*?(?:\.|;|$)",

        "Arbitration": r"(?:arbitration|arbitrator|dispute resolution).*?(?:\.|;|$)",

        "Intellectual Property": r"(?:intellectual property|copyright|trademark|patent).*?(?:\.|;|$)",

        "Payment Terms": r"(?:payment|remuneration|consideration|fees?).*?(?:\.|;|$)"

    }



    found_clauses = {}

    for clause, pattern in clause_patterns.items():

        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)

        if matches:

            found_clauses[clause] = [match.strip() for match in matches[:3]]  # Limit to 3 matches



    return found_clauses



def analyze_sentiment(text):

    """Analyze document sentiment"""

    if not text or 'sentiment' not in models:

        return {}



    try:

        sentences = sent_tokenize(text)

        sentiments = []



        for sentence in sentences[:50]:  # Analyze first 50 sentences

            score = models['sentiment'].polarity_scores(sentence)

            sentiments.append(score)



        # Calculate overall sentiment

        avg_sentiment = {

            'compound': sum(s['compound'] for s in sentiments) / len(sentiments),

            'positive': sum(s['pos'] for s in sentiments) / len(sentiments),

            'negative': sum(s['neg'] for s in sentiments) / len(sentiments),

            'neutral': sum(s['neu'] for s in sentiments) / len(sentiments)

        }



        return avg_sentiment

    except:

        return {}



def generate_word_cloud(text):

    """Generate word cloud from document text"""

    if not text:

        return None



    try:

        # Remove stopwords and clean text

        stop_words = set(stopwords.words('english'))

        words = word_tokenize(text.lower())

        filtered_words = [word for word in words if word.isalpha() and word not in stop_words and len(word) > 3]



        if not filtered_words:

            return None



        wordcloud = WordCloud(width=800, height=400, background_color='white').generate(' '.join(filtered_words))



        fig, ax = plt.subplots(figsize=(10, 5))

        ax.imshow(wordcloud, interpolation='bilinear')

        ax.axis('off')



        return fig

    except:

        return None



def stream_response(text):

    """Stream response word by word"""

    for word in text.split():

        yield word + " "

        time.sleep(0.05)



# Sidebar

with st.sidebar:

    st.markdown("""

    <div class="main-header">

        <h2>⚖️ AI Legal Analyzer</h2>

        <p>Advanced Document Intelligence</p>

    </div>

    """, unsafe_allow_html=True)



    st.header("📄 Upload Document")

    st.markdown("Upload a legal document for comprehensive analysis")

    uploaded_file = st.file_uploader("Choose a file", type=["pdf", "docx"], label_visibility="collapsed")



    # Analysis options

    st.header("🔧 Analysis Options")

    analysis_options = st.multiselect(

        "Select analysis types:",

        ["Summary", "Named Entities", "Legal Clauses", "Sentiment Analysis", "Document Structure", "Word Cloud"],

        default=["Summary", "Named Entities", "Legal Clauses"]

    )



    # Advanced settings

    with st.expander("⚙️ Advanced Settings"):

        max_summary_length = st.slider("Summary Length", 100, 500, 256)

        entity_confidence = st.slider("Entity Confidence Threshold", 0.5, 1.0, 0.8)

        include_context = st.checkbox("Include Context in Entities", True)



    if st.button("🗑️ Clear Analysis", type="secondary"):

        if "collection_name" in st.session_state:

            try:

                qdrant_client.delete_collection(collection_name=st.session_state.collection_name)

                st.success(f"Collection deleted successfully!")

            except Exception as e:

                st.error(f"Error deleting collection: {e}")



        # Clear session state

        for key in list(st.session_state.keys()):

            del st.session_state[key]

        st.rerun()



    st.markdown("---")

    st.markdown("### 🤖 Model Status")

    if models:

        st.success(f"✅ {len(models)} models loaded")

        for model_name in models.keys():

            st.markdown(f"• {model_name.replace('_', ' ').title()}")

    else:

        st.error("❌ Models not loaded")



# Main header

st.markdown("""

<div class="main-header">

    <h1>⚖️ AI Legal Document Analyzer</h1>

    <p>Comprehensive Legal Document Intelligence Platform</p>

</div>

""", unsafe_allow_html=True)



# Initialize session state

if "messages" not in st.session_state:

    st.session_state.messages = [{"role": "ai", "content": "👋 Welcome! Upload a legal document to begin comprehensive analysis."}]

if "collection_name" not in st.session_state:

    st.session_state.collection_name = None

if "last_processed_file" not in st.session_state:

    st.session_state.last_processed_file = None

if "analysis_results" not in st.session_state:

    st.session_state.analysis_results = {}



# Process uploaded file

if uploaded_file is not None and st.session_state.last_processed_file != uploaded_file.name:

    with st.spinner("🔄 Processing document..."):

        st.session_state.document_text = extract_text_from_file(uploaded_file)

        st.session_state.last_processed_file = uploaded_file.name



        # Clear previous results

        st.session_state.messages = []

        st.session_state.analysis_results = {}

        st.session_state.collection_name = None



        if st.session_state.document_text:

            # Embed document for RAG

            try:

                st.session_state.collection_name = embedtxt(st.session_state.document_text, uploaded_file.name)

            except Exception as e:

                st.error(f"Failed to embed document: {e}")



            if st.session_state.collection_name:

                # Perform selected analyses

                results = {}



                if "Summary" in analysis_options and 'summarizer' in models:

                    model, tokenizer = models['summarizer']

                    results['summary'] = generate_summary(st.session_state.document_text, model, tokenizer)



                if "Named Entities" in analysis_options:

                    results['entities'] = advanced_entity_extraction(st.session_state.document_text)



                if "Legal Clauses" in analysis_options:

                    results['clauses'] = find_legal_clauses(st.session_state.document_text)



                if "Sentiment Analysis" in analysis_options:

                    results['sentiment'] = analyze_sentiment(st.session_state.document_text)



                if "Document Structure" in analysis_options:

                    results['structure'] = analyze_document_structure(st.session_state.document_text)



                if "Word Cloud" in analysis_options:

                    results['wordcloud'] = generate_word_cloud(st.session_state.document_text)



                st.session_state.analysis_results = results



                # Create initial message

                if results.get('summary'):

                    st.session_state.messages.append({

                        "role": "ai",

                        "content": f"📄 **Analysis Complete for `{uploaded_file.name}`**\n\n**Summary:**\n{results['summary']}"

                    })

        else:

            error_message = f"❌ Could not extract text from `{uploaded_file.name}`."

            st.session_state.messages.append({"role": "ai", "content": error_message})



# Display analysis results

if "analysis_results" in st.session_state and st.session_state.analysis_results:

    results = st.session_state.analysis_results



    # Create tabs for different analyses

    tab_names = []

    if 'entities' in results: tab_names.append("🏷️ Named Entities")

    if 'clauses' in results: tab_names.append("📋 Legal Clauses")

    if 'sentiment' in results: tab_names.append("😊 Sentiment")

    if 'structure' in results: tab_names.append("📊 Structure")

    if 'wordcloud' in results: tab_names.append("☁️ Word Cloud")



    if tab_names:

        tabs = st.tabs(tab_names)

        tab_idx = 0



        # Named Entities Tab

        if 'entities' in results:

            with tabs[tab_idx]:

                entities = results['entities']

                if entities:

                    # Create metrics

                    col1, col2, col3, col4 = st.columns(4)

                    with col1:

                        st.metric("Total Categories", len(entities))

                    with col2:

                        total_entities = sum(len(ents) for ents in entities.values())

                        st.metric("Total Entities", total_entities)

                    with col3:

                        most_common = max(entities.keys(), key=lambda k: len(entities[k])) if entities else "None"

                        st.metric("Most Common Type", most_common)

                    with col4:

                        avg_per_type = total_entities / len(entities) if entities else 0

                        st.metric("Avg per Type", f"{avg_per_type:.1f}")



                    # Display entities with styling

                    for label, entity_list in entities.items():

                        if entity_list:

                            st.markdown(f"**{label.replace('_', ' ').title()}** ({len(entity_list)} found)")

                            entity_html = ""

                            for entity in entity_list[:10]:  # Limit display

                                css_class = {

                                    'PERSON': 'person-tag', 'PER': 'person-tag',

                                    'ORG': 'org-tag', 'ORGANIZATION': 'org-tag',

                                    'GPE': 'location-tag', 'LOC': 'location-tag',

                                    'DATE': 'date-tag', 'TIME': 'date-tag',

                                    'MONEY': 'money-tag', 'Money_Amounts': 'money-tag',

                                    'LAW': 'law-tag', 'Legal_Citations': 'law-tag'

                                }.get(label, 'entity-tag')

                                entity_html += f'<span class="{css_class}">{entity}</span> '

                            st.markdown(entity_html, unsafe_allow_html=True)

                            if len(entity_list) > 10:

                                st.caption(f"... and {len(entity_list) - 10} more")

                            st.markdown("---")

                else:

                    st.info("No named entities found in the document.")

            tab_idx += 1



        # Legal Clauses Tab

        if 'clauses' in results:

            with tabs[tab_idx]:

                clauses = results['clauses']

                if clauses:

                    st.metric("Clause Types Found", len(clauses))

                    for clause_type, clause_list in clauses.items():

                        with st.expander(f"📋 {clause_type} ({len(clause_list)} found)", expanded=True):

                            for i, clause in enumerate(clause_list, 1):

                                st.markdown(f"**{i}.** {clause}")

                else:

                    st.info("No legal clauses detected in the document.")

            tab_idx += 1



        # Sentiment Tab

        if 'sentiment' in results:

            with tabs[tab_idx]:

                sentiment = results['sentiment']

                if sentiment:

                    col1, col2 = st.columns(2)

                    with col1:

                        # Sentiment metrics

                        st.metric("Overall Sentiment",

                                f"{'Positive' if sentiment['compound'] > 0.1 else 'Negative' if sentiment['compound'] < -0.1 else 'Neutral'}")

                        st.metric("Compound Score", f"{sentiment['compound']:.3f}")



                    with col2:

                        # Sentiment breakdown chart

                        sentiment_data = pd.DataFrame({

                            'Sentiment': ['Positive', 'Negative', 'Neutral'],

                            'Score': [sentiment['positive'], sentiment['negative'], sentiment['neutral']]

                        })

                        fig = px.pie(sentiment_data, values='Score', names='Sentiment',

                                   title="Sentiment Distribution")

                        st.plotly_chart(fig, use_container_width=True)

                else:

                    st.info("Sentiment analysis not available.")

            tab_idx += 1



        # Document Structure Tab

        if 'structure' in results:

            with tabs[tab_idx]:

                structure = results['structure']

                if structure:

                    col1, col2 = st.columns(2)

                    with col1:

                        st.metric("Total Sections", structure.get('total_sections', 0))

                    with col2:

                        st.metric("Total Lines", structure.get('total_lines', 0))



                    if structure.get('sections'):

                        st.markdown("### Document Sections")

                        for i, section in enumerate(structure['sections'][:10], 1):

                            with st.expander(f"Section {i}: {section['title'][:50]}..."):

                                st.markdown(f"**Content Preview:**")

                                content_preview = ' '.join(section['content'][:3])[:200] + "..."

                                st.markdown(content_preview)

                else:

                    st.info("Document structure analysis not available.")

            tab_idx += 1



        # Word Cloud Tab

        if 'wordcloud' in results:

            with tabs[tab_idx]:

                wordcloud_fig = results['wordcloud']

                if wordcloud_fig:

                    st.pyplot(wordcloud_fig)

                else:

                    st.info("Word cloud could not be generated.")



st.markdown("---")

st.header("💬 Chat With Your Document")



# Display chat messages

for message in st.session_state.messages:

    with st.chat_message(message["role"]):

        st.markdown(message["content"])



# Chat input box

if prompt := st.chat_input("Ask a question about your document..."):

    with st.chat_message("human"):

        st.markdown(prompt)

    st.session_state.messages.append({"role": "human", "content": prompt})



    with st.chat_message("ai"):

        if st.session_state.collection_name:

            full_response = st.write_stream(ai_response(prompt, st.session_state.collection_name))

        else:

            response_text = "Please upload a document first to chat with it."

            full_response = st.write_stream(stream_response(response_text))



    # Convert generator to string if needed

    if hasattr(full_response, '__iter__') and not isinstance(full_response, str):

        full_response = ''.join(full_response)



    st.session_state.messages.append({"role": "ai", "content": str(full_response)})





this is working ner code so make it work properly and make call 3 times to llm output for like summary , ner, legal clauses so it can be showed in the separate section and normal nlp model ner and legal clauses shows in the another two section